import { NextResponse } from 'next/server';
import { db } from '@/lib/db';
import { env } from '@/lib/env';

export async function GET() {
  const timestamp = new Date().toISOString();

  try {
    // Test database connection with timeout
    await Promise.race([
      db.execute('SELECT 1 as test'),
      new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Database timeout')), 5000)
      )
    ]);

    // Test environment variables
    const envCheck = {
      DATABASE_URL: !!env.DATABASE_URL,
      NEXTAUTH_SECRET: !!env.NEXTAUTH_SECRET,
      NEXTAUTH_URL: !!env.NEXTAUTH_URL,
      ANTHROPIC_API_KEY: !!env.ANTHROPIC_API_KEY,
      NODE_ENV: env.NODE_ENV,
    };

    return NextResponse.json({
      status: 'healthy',
      database: 'connected',
      environment: envCheck,
      timestamp,
      version: '1.0.0',
      deployment: {
        platform: process.env.VERCEL ? 'vercel' : 'other',
        region: process.env.VERCEL_REGION || 'unknown',
      }
    });
  } catch (error) {
    console.error('Health check failed:', error);

    const errorMessage = error instanceof Error ? error.message : 'Unknown error';

    return NextResponse.json(
      {
        status: 'unhealthy',
        database: 'disconnected',
        error: errorMessage,
        timestamp,
        environment: {
          DATABASE_URL: !!process.env.DATABASE_URL,
          NEXTAUTH_SECRET: !!process.env.NEXTAUTH_SECRET,
          NEXTAUTH_URL: !!process.env.NEXTAUTH_URL,
          NODE_ENV: process.env.NODE_ENV,
        },
        deployment: {
          platform: process.env.VERCEL ? 'vercel' : 'other',
          region: process.env.VERCEL_REGION || 'unknown',
        }
      },
      { status: 503 }
    );
  }
}

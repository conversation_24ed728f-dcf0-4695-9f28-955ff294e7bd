/**
 * Environment variable validation for production deployment
 */

interface EnvironmentConfig {
  DATABASE_URL: string;
  NEXTAUTH_SECRET: string;
  NEXTAUTH_URL: string;
  ANTHROPIC_API_KEY?: string;
  ADMIN_EMAIL?: string;
  ADMIN_PASSWORD?: string;
  NEXT_PUBLIC_APP_URL?: string;
  NODE_ENV: 'development' | 'production' | 'test';
}

function validateEnvironment(): EnvironmentConfig {
  const requiredVars = [
    'DATABASE_URL',
    'NEXTAUTH_SECRET',
    'NEXTAUTH_URL',
  ];

  // In production, admin credentials are also required
  if (process.env.NODE_ENV === 'production') {
    requiredVars.push('ADMIN_EMAIL', 'ADMIN_PASSWORD');
  }

  const missingVars = requiredVars.filter(varName => !process.env[varName]);

  if (missingVars.length > 0) {
    throw new Error(
      `Missing required environment variables: ${missingVars.join(', ')}\n` +
      'Please check your .env.local file or Vercel environment variables.'
    );
  }

  // Validate DATABASE_URL format
  const databaseUrl = process.env.DATABASE_URL!;
  if (!databaseUrl.startsWith('postgresql://') && !databaseUrl.startsWith('postgres://')) {
    throw new Error('DATABASE_URL must be a valid PostgreSQL connection string');
  }

  // Validate NEXTAUTH_URL format
  const nextAuthUrl = process.env.NEXTAUTH_URL!;
  try {
    new URL(nextAuthUrl);
  } catch {
    throw new Error('NEXTAUTH_URL must be a valid URL');
  }

  // Validate NEXTAUTH_SECRET length
  const nextAuthSecret = process.env.NEXTAUTH_SECRET!;
  if (nextAuthSecret.length < 32) {
    throw new Error('NEXTAUTH_SECRET must be at least 32 characters long');
  }

  return {
    DATABASE_URL: databaseUrl,
    NEXTAUTH_SECRET: nextAuthSecret,
    NEXTAUTH_URL: nextAuthUrl,
    ANTHROPIC_API_KEY: process.env.ANTHROPIC_API_KEY,
    ADMIN_EMAIL: process.env.ADMIN_EMAIL,
    ADMIN_PASSWORD: process.env.ADMIN_PASSWORD,
    NEXT_PUBLIC_APP_URL: process.env.NEXT_PUBLIC_APP_URL,
    NODE_ENV: (process.env.NODE_ENV as 'development' | 'production' | 'test') || 'development',
  };
}

// Validate environment on module load
export const env = validateEnvironment();

// Helper function to check if we're in production
export const isProduction = env.NODE_ENV === 'production';

// Helper function to check if we're in development
export const isDevelopment = env.NODE_ENV === 'development';

// Helper function to get the base URL
export const getBaseUrl = () => {
  if (env.NEXT_PUBLIC_APP_URL) {
    return env.NEXT_PUBLIC_APP_URL;
  }

  if (env.NEXTAUTH_URL) {
    return env.NEXTAUTH_URL;
  }

  return 'http://localhost:3000';
};

export default env;

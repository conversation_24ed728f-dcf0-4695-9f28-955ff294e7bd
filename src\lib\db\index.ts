import { drizzle } from 'drizzle-orm/postgres-js';
import postgres from 'postgres';
import * as schema from './schema';

if (!process.env.DATABASE_URL) {
  throw new Error('DATABASE_URL environment variable is not set');
}

const connectionString = process.env.DATABASE_URL;

// Optimized for Vercel serverless environment
const client = postgres(connectionString, {
  prepare: false,
  ssl: process.env.NODE_ENV === 'production' ? 'require' : false,
  max: 1, // Single connection for serverless
  idle_timeout: 20,
  connect_timeout: 10,
  max_lifetime: 60 * 30, // 30 minutes
  connection: {
    application_name: 'ielts-certification-system'
  },
  // Vercel-specific optimizations
  transform: {
    undefined: null,
  },
  // Handle connection errors gracefully
  onnotice: () => {}, // Suppress notices in production
});

export const db = drizzle(client, { schema });

export * from './schema';

import { NextResponse } from 'next/server';
import { db } from '@/lib/db';
import { users } from '@/lib/db/schema';
import { eq } from 'drizzle-orm';
import bcrypt from 'bcryptjs';

/**
 * Production setup endpoint
 * This endpoint should be called once after deployment to set up initial users
 * For security, it only works if no admin users exist
 */
export async function POST() {
  try {
    // Check if this is a fresh deployment (no admin users exist)
    const existingAdmins = await db
      .select()
      .from(users)
      .where(eq(users.role, 'admin'))
      .limit(1);

    if (existingAdmins.length > 0) {
      return NextResponse.json(
        { 
          error: 'Setup already completed',
          message: 'Admin users already exist. This endpoint can only be used for initial setup.'
        },
        { status: 400 }
      );
    }

    // Validate required environment variables
    const adminEmail = process.env.ADMIN_EMAIL;
    const adminPassword = process.env.ADMIN_PASSWORD;

    if (!adminEmail || !adminPassword) {
      return NextResponse.json(
        { 
          error: 'Missing configuration',
          message: 'ADMIN_EMAIL and ADMIN_PASSWORD environment variables are required'
        },
        { status: 500 }
      );
    }

    // Create admin user
    const hashedAdminPassword = await bcrypt.hash(adminPassword, 12);
    
    await db.insert(users).values({
      name: 'System Administrator',
      email: adminEmail,
      password: hashedAdminPassword,
      role: 'admin',
    });

    // Create test checker user
    const testCheckerEmail = '<EMAIL>';
    const testCheckerPassword = 'checker123';
    const hashedCheckerPassword = await bcrypt.hash(testCheckerPassword, 12);

    await db.insert(users).values({
      name: 'Test Checker',
      email: testCheckerEmail,
      password: hashedCheckerPassword,
      role: 'test_checker',
    });

    return NextResponse.json({
      success: true,
      message: 'Database setup completed successfully',
      users: [
        { email: adminEmail, role: 'admin' },
        { email: testCheckerEmail, role: 'test_checker' }
      ]
    });

  } catch (error) {
    console.error('Setup failed:', error);
    
    return NextResponse.json(
      { 
        error: 'Setup failed',
        message: error instanceof Error ? error.message : 'Unknown error occurred'
      },
      { status: 500 }
    );
  }
}

// GET method to check setup status
export async function GET() {
  try {
    const adminCount = await db
      .select()
      .from(users)
      .where(eq(users.role, 'admin'));

    const checkerCount = await db
      .select()
      .from(users)
      .where(eq(users.role, 'test_checker'));

    return NextResponse.json({
      setupComplete: adminCount.length > 0,
      adminUsers: adminCount.length,
      checkerUsers: checkerCount.length,
      message: adminCount.length > 0 
        ? 'Setup is complete' 
        : 'Setup is required - call POST /api/setup'
    });

  } catch (error) {
    console.error('Setup check failed:', error);
    
    return NextResponse.json(
      { 
        error: 'Setup check failed',
        message: error instanceof Error ? error.message : 'Unknown error occurred'
      },
      { status: 500 }
    );
  }
}

{"name": "ielts-certification-system", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate", "db:studio": "drizzle-kit studio", "db:setup": "tsx scripts/setup-db.ts", "db:production-setup": "tsx scripts/production-setup.ts", "validate-deployment": "tsx scripts/validate-deployment.ts", "vercel-build": "next build", "postbuild": "echo 'Build completed successfully'", "type-check": "tsc --noEmit", "test:health": "curl -f http://localhost:3000/api/health || exit 1"}, "dependencies": {"@anthropic-ai/sdk": "^0.52.0", "@auth/drizzle-adapter": "^1.9.1", "@hookform/resolvers": "^5.0.1", "@paralleldrive/cuid2": "^2.2.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-toast": "^1.2.14", "@types/bcryptjs": "^2.4.6", "@types/pg": "^8.15.2", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dotenv": "^16.5.0", "drizzle-kit": "^0.31.1", "drizzle-orm": "^0.43.1", "html2canvas": "^1.4.1", "jspdf": "^3.0.1", "lucide-react": "^0.511.0", "next": "15.1.8", "next-auth": "^5.0.0-beta.28", "postcss": "^8", "postgres": "^3.4.7", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.56.4", "tailwind-merge": "^3.3.0", "tailwindcss": "^3.4.1", "zod": "^3.25.28"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.1.8", "tsx": "^4.19.0", "typescript": "^5"}}
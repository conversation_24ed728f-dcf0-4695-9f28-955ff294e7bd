# Vercel Deployment Guide

This guide will help you deploy the IELTS Certification System to Vercel successfully.

## Prerequisites

1. **Vercel Account**: Sign up at [vercel.com](https://vercel.com)
2. **GitHub Repository**: Your code should be in a GitHub repository
3. **PostgreSQL Database**: A production PostgreSQL database (recommended: Neon, Supabase, or Railway)

## Step 1: Database Setup

### Option A: Neon (Recommended)
1. Go to [neon.tech](https://neon.tech) and create an account
2. Create a new project
3. Copy the connection string (it should look like: `************************************************************`)

### Option B: Supabase
1. Go to [supabase.com](https://supabase.com) and create an account
2. Create a new project
3. Go to Settings > Database and copy the connection string

### Option C: Railway
1. Go to [railway.app](https://railway.app) and create an account
2. Create a new PostgreSQL service
3. Copy the connection string from the service details

## Step 2: Environment Variables

Set up the following environment variables in Vercel:

### Required Variables
```bash
# Database
DATABASE_URL=postgresql://your-connection-string

# Authentication (Generate a secure secret)
NEXTAUTH_SECRET=your-32-character-or-longer-secret-key
NEXTAUTH_URL=https://your-app-name.vercel.app

# Admin Credentials
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=your-secure-admin-password
```

### Optional Variables
```bash
# AI Integration (if using Claude API)
ANTHROPIC_API_KEY=your-claude-api-key

# Public URL
NEXT_PUBLIC_APP_URL=https://your-app-name.vercel.app
```

### How to Generate NEXTAUTH_SECRET
Run this command in your terminal:
```bash
openssl rand -base64 32
```

## Step 3: Deploy to Vercel

### Method 1: Vercel Dashboard
1. Go to [vercel.com/dashboard](https://vercel.com/dashboard)
2. Click "New Project"
3. Import your GitHub repository
4. Configure the project:
   - **Framework Preset**: Next.js
   - **Build Command**: `npm run build`
   - **Output Directory**: `.next`
   - **Install Command**: `npm ci`

### Method 2: Vercel CLI
```bash
# Install Vercel CLI
npm i -g vercel

# Login to Vercel
vercel login

# Deploy
vercel --prod
```

## Step 4: Set Environment Variables in Vercel

1. Go to your project dashboard on Vercel
2. Click on "Settings" tab
3. Click on "Environment Variables"
4. Add each environment variable:
   - Name: `DATABASE_URL`
   - Value: Your database connection string
   - Environment: Production, Preview, Development
5. Repeat for all required variables

## Step 5: Database Migration

After deployment, you need to run database migrations:

### Option A: Using Vercel CLI
```bash
# Set environment variables locally for migration
export DATABASE_URL="your-production-database-url"

# Run migrations
npm run db:migrate

# Set up initial data
npm run db:setup
```

### Option B: Using Vercel Functions
1. Create a temporary API endpoint for migration
2. Visit the endpoint once to run migrations
3. Delete the endpoint after use

## Step 6: Verify Deployment

1. Visit your deployed app: `https://your-app-name.vercel.app`
2. Check the health endpoint: `https://your-app-name.vercel.app/api/health`
3. Try logging in with your admin credentials

## Troubleshooting

### Common Issues

#### 1. "Server Error" on Login
- Check that `NEXTAUTH_SECRET` is set and at least 32 characters
- Verify `NEXTAUTH_URL` matches your Vercel app URL
- Check database connection in health endpoint

#### 2. Database Connection Failed
- Verify `DATABASE_URL` is correct
- Ensure database allows connections from Vercel IPs
- Check if SSL is required (add `?sslmode=require` to connection string)

#### 3. Environment Variables Not Working
- Make sure variables are set for all environments (Production, Preview, Development)
- Redeploy after adding environment variables
- Check variable names for typos

#### 4. Build Failures
- Check that all dependencies are in `package.json`
- Verify TypeScript types are correct
- Check for any missing files

### Debug Steps

1. **Check Health Endpoint**
   ```
   GET https://your-app-name.vercel.app/api/health
   ```

2. **Check Vercel Logs**
   - Go to Vercel dashboard
   - Click on your project
   - Go to "Functions" tab
   - Check logs for errors

3. **Test Database Connection**
   ```bash
   # Test locally with production database
   export DATABASE_URL="your-production-url"
   npm run dev
   ```

## Security Considerations

1. **Environment Variables**: Never commit sensitive data to your repository
2. **Database Access**: Restrict database access to necessary IPs only
3. **HTTPS**: Always use HTTPS in production (Vercel provides this automatically)
4. **Secrets**: Use strong, unique secrets for `NEXTAUTH_SECRET`

## Performance Optimization

1. **Database Connection Pooling**: Already configured for Vercel serverless
2. **Image Optimization**: Configured in `next.config.ts`
3. **Function Timeouts**: Set in `vercel.json` for different API routes

## Support

If you encounter issues:
1. Check the health endpoint for diagnostic information
2. Review Vercel function logs
3. Verify all environment variables are set correctly
4. Test database connection independently

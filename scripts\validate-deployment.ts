#!/usr/bin/env tsx

/**
 * Deployment validation script
 * Validates that all required environment variables are set for production deployment
 */

interface ValidationResult {
  check: string;
  status: 'pass' | 'fail' | 'warning';
  message: string;
  suggestion?: string;
}

function validateEnvironment(): ValidationResult[] {
  const results: ValidationResult[] = [];
  const isProduction = process.env.NODE_ENV === 'production';
  const isVercel = !!process.env.VERCEL;

  // Required environment variables for production
  const requiredVars = [
    'NEXTAUTH_SECRET',
    'NEXTAUTH_URL',
    'DATABASE_URL',
    'ADMIN_EMAIL',
    'ADMIN_PASSWORD'
  ];

  // Check each required variable
  requiredVars.forEach(varName => {
    const value = process.env[varName];
    if (!value) {
      // Only fail for missing vars in production/Vercel environment
      if (isProduction || isVercel) {
        results.push({
          check: `Environment Variable: ${varName}`,
          status: 'fail',
          message: `${varName} is not set`,
          suggestion: `Set ${varName} in your Vercel environment variables`
        });
      } else {
        results.push({
          check: `Environment Variable: ${varName}`,
          status: 'warning',
          message: `${varName} is not set (OK for local development)`,
          suggestion: `Will be required in production - set in Vercel environment variables`
        });
      }
    } else {
      results.push({
        check: `Environment Variable: ${varName}`,
        status: 'pass',
        message: `${varName} is set`
      });
    }
  });

  // Validate NEXTAUTH_SECRET length
  const nextAuthSecret = process.env.NEXTAUTH_SECRET;
  if (nextAuthSecret) {
    if (nextAuthSecret.length < 32) {
      results.push({
        check: 'NEXTAUTH_SECRET Length',
        status: 'fail',
        message: `NEXTAUTH_SECRET is only ${nextAuthSecret.length} characters`,
        suggestion: 'NEXTAUTH_SECRET should be at least 32 characters. Generate with: openssl rand -base64 32'
      });
    } else {
      results.push({
        check: 'NEXTAUTH_SECRET Length',
        status: 'pass',
        message: `NEXTAUTH_SECRET is ${nextAuthSecret.length} characters (good)`
      });
    }
  }

  // Validate DATABASE_URL format
  const databaseUrl = process.env.DATABASE_URL;
  if (databaseUrl) {
    if (databaseUrl.startsWith('postgresql://') || databaseUrl.startsWith('postgres://')) {
      results.push({
        check: 'DATABASE_URL Format',
        status: 'pass',
        message: 'DATABASE_URL format looks correct'
      });
    } else {
      results.push({
        check: 'DATABASE_URL Format',
        status: 'fail',
        message: 'DATABASE_URL does not appear to be a PostgreSQL connection string',
        suggestion: 'DATABASE_URL should start with postgresql:// or postgres://'
      });
    }
  }

  // Validate NEXTAUTH_URL format
  const nextAuthUrl = process.env.NEXTAUTH_URL;
  if (nextAuthUrl) {
    try {
      new URL(nextAuthUrl);
      results.push({
        check: 'NEXTAUTH_URL Format',
        status: 'pass',
        message: 'NEXTAUTH_URL format is valid'
      });
    } catch {
      results.push({
        check: 'NEXTAUTH_URL Format',
        status: 'fail',
        message: 'NEXTAUTH_URL is not a valid URL',
        suggestion: 'NEXTAUTH_URL should be a complete URL like https://your-app.vercel.app'
      });
    }
  }

  return results;
}

function main() {
  console.log('🔍 IELTS Certification System - Deployment Validation\n');

  const isProduction = process.env.NODE_ENV === 'production';
  const isVercel = !!process.env.VERCEL;

  if (isProduction || isVercel) {
    console.log('🚀 Running in production/Vercel environment - strict validation\n');
  } else {
    console.log('🛠️  Running in development environment - relaxed validation\n');
  }

  const results = validateEnvironment();

  let passed = 0;
  let failed = 0;
  let warnings = 0;

  results.forEach(result => {
    const icon = result.status === 'pass' ? '✅' : result.status === 'fail' ? '❌' : '⚠️';
    console.log(`${icon} ${result.check}: ${result.message}`);

    if (result.suggestion) {
      console.log(`   💡 ${result.suggestion}`);
    }

    if (result.status === 'pass') passed++;
    else if (result.status === 'fail') failed++;
    else warnings++;
  });

  console.log(`\n📊 Summary:`);
  console.log(`   ✅ Passed: ${passed}`);
  console.log(`   ❌ Failed: ${failed}`);
  console.log(`   ⚠️  Warnings: ${warnings}`);

  if (failed > 0) {
    console.log('\n❌ Deployment validation failed. Please fix the issues above before deploying.');
    process.exit(1);
  } else if (warnings > 0 && !isProduction && !isVercel) {
    console.log('\n⚠️  Validation passed with warnings. Environment variables will be required in production.');
    console.log('📋 Make sure to set all required variables in Vercel before deploying.');
    process.exit(0);
  } else {
    console.log('\n✅ Deployment validation passed! Ready to deploy to Vercel.');
    process.exit(0);
  }
}

if (require.main === module) {
  main();
}

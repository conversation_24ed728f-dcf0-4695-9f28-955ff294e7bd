'use client';

import { useState, useEffect, useCallback } from 'react';
import { useParams } from 'next/navigation';
import Link from 'next/link';
import {
  ArrowLeft,
  MessageCircle,
  Brain,
  Target,
  TrendingUp,
  Star,
  BookOpen,
  Lightbulb,
  Headphones,
  Eye,
  PenTool,
  GraduationCap,
  AlertCircle
} from 'lucide-react';
import HorizontalNavigationMenu from '@/components/results/HorizontalNavigationMenu';

interface TestResult {
  id: string;
  listeningBandScore: number | null;
  readingBandScore: number | null;
  writingBandScore: number | null;
  speakingBandScore: number | null;
  overallBandScore: number | null;
  aiFeedbackGenerated: boolean;
  certificateGenerated: boolean;
  candidate: {
    fullName: string;
  };
}

interface AIFeedback {
  id: string;
  testResultId: string;
  listeningFeedback: string | null;
  readingFeedback: string | null;
  writingFeedback: string | null;
  speakingFeedback: string | null;
  overallFeedback: string | null;
  studyRecommendations: string | null;
  generatedAt: string;
}

export default function FeedbackPage() {
  const params = useParams();
  const resultId = params.id as string;

  const [result, setResult] = useState<TestResult | null>(null);
  const [feedback, setFeedback] = useState<AIFeedback | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [feedbackLoading, setFeedbackLoading] = useState(false);
  const [error, setError] = useState('');

  const fetchResult = useCallback(async () => {
    try {
      const response = await fetch(`/api/results/${resultId}`);
      if (response.ok) {
        const data = await response.json();
        setResult(data);
      } else {
        const errorData = await response.json();
        setError(errorData.error || 'Result not found');
      }
    } catch (error) {
      console.error('Error fetching result:', error);
      setError('Failed to load result');
    } finally {
      setIsLoading(false);
    }
  }, [resultId]);

  const fetchFeedback = useCallback(async () => {
    if (!result?.aiFeedbackGenerated) return;

    setFeedbackLoading(true);
    try {
      const response = await fetch(`/api/feedback/${resultId}`);
      if (response.ok) {
        const data = await response.json();
        setFeedback(data);
      }
    } catch (error) {
      console.error('Error fetching feedback:', error);
    } finally {
      setFeedbackLoading(false);
    }
  }, [resultId, result?.aiFeedbackGenerated]);

  useEffect(() => {
    fetchResult();
  }, [fetchResult]);

  useEffect(() => {
    if (result) {
      fetchFeedback();
    }
  }, [fetchFeedback, result]);

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading feedback...</p>
        </div>
      </div>
    );
  }

  if (error || !result) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center max-w-md mx-auto p-8">
          <AlertCircle className="h-16 w-16 text-red-400 mx-auto mb-4" />
          <h3 className="text-xl font-medium text-gray-900 mb-2">Unable to Load Feedback</h3>
          <p className="text-gray-600 mb-6">{error || 'Result not found'}</p>
          <Link
            href="/search"
            className="inline-flex items-center text-blue-600 hover:text-blue-700 font-medium"
          >
            <ArrowLeft className="h-4 w-4 mr-1" />
            Back to Search
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center">
              <Link href="/search" className="flex items-center text-blue-600 hover:text-blue-700 mr-4">
                <ArrowLeft className="h-5 w-5 mr-1" />
                Back to Search
              </Link>
              <MessageCircle className="h-8 w-8 text-blue-600 mr-3" />
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Detailed Feedback</h1>
                <p className="text-gray-600">Performance Analysis & Recommendations</p>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Horizontal Navigation */}
      <HorizontalNavigationMenu
        resultId={resultId}
        completionStatus={{
          overview: true,
          progress: !!result.overallBandScore,
          feedback: result.aiFeedbackGenerated,
          certificate: result.certificateGenerated
        }}
      />

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="space-y-8">
            {/* Performance Overview */}
            <div className="bg-white shadow rounded-lg p-6">
              <h2 className="text-lg font-medium text-gray-900 mb-6">Performance Overview</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Strengths */}
                <div className="bg-green-50 rounded-lg p-6">
                  <h3 className="font-semibold text-green-800 mb-3 flex items-center">
                    <Star className="h-5 w-5 mr-2" />
                    Your Strengths
                  </h3>
                  <div className="space-y-2 text-sm text-green-700">
                    {result.listeningBandScore && result.listeningBandScore >= 7 && (
                      <p>• Strong listening comprehension skills</p>
                    )}
                    {result.readingBandScore && result.readingBandScore >= 7 && (
                      <p>• Excellent reading comprehension abilities</p>
                    )}
                    {result.writingBandScore && result.writingBandScore >= 7 && (
                      <p>• Effective written communication skills</p>
                    )}
                    {result.speakingBandScore && result.speakingBandScore >= 7 && (
                      <p>• Confident oral communication abilities</p>
                    )}
                    {result.overallBandScore && result.overallBandScore >= 7 && (
                      <p>• Overall strong English proficiency</p>
                    )}
                    {![result.listeningBandScore, result.readingBandScore, result.writingBandScore, result.speakingBandScore]
                      .some(score => score && score >= 7) && (
                      <p>• Demonstrates foundational English skills</p>
                    )}
                  </div>
                </div>

                {/* Areas for Improvement */}
                <div className="bg-orange-50 rounded-lg p-6">
                  <h3 className="font-semibold text-orange-800 mb-3 flex items-center">
                    <Target className="h-5 w-5 mr-2" />
                    Areas for Improvement
                  </h3>
                  <div className="space-y-2 text-sm text-orange-700">
                    {result.listeningBandScore && result.listeningBandScore < 6.5 && (
                      <p>• Focus on listening comprehension practice</p>
                    )}
                    {result.readingBandScore && result.readingBandScore < 6.5 && (
                      <p>• Enhance reading speed and comprehension</p>
                    )}
                    {result.writingBandScore && result.writingBandScore < 6.5 && (
                      <p>• Develop writing structure and vocabulary</p>
                    )}
                    {result.speakingBandScore && result.speakingBandScore < 6.5 && (
                      <p>• Practice speaking fluency and pronunciation</p>
                    )}
                    {result.overallBandScore && result.overallBandScore >= 6.5 && result.overallBandScore < 7.5 && (
                      <>
                        <p>• Expand academic vocabulary range</p>
                        <p>• Practice complex grammatical structures</p>
                        <p>• Improve consistency across all modules</p>
                      </>
                    )}
                    {result.overallBandScore && result.overallBandScore >= 7.5 && (
                      <>
                        <p>• Fine-tune pronunciation and intonation</p>
                        <p>• Master advanced vocabulary and idioms</p>
                        <p>• Perfect essay coherence and cohesion</p>
                      </>
                    )}
                  </div>
                </div>
              </div>
            </div>

            {/* AI-Generated Feedback */}
            {result.aiFeedbackGenerated && (
              <div className="bg-white shadow rounded-lg p-6">
                <div className="flex items-center mb-6">
                  <Brain className="h-6 w-6 text-purple-600 mr-2" />
                  <h2 className="text-lg font-medium text-gray-900">AI-Generated Detailed Feedback</h2>
                </div>

                {feedbackLoading ? (
                  <div className="text-center py-8">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600 mx-auto mb-4"></div>
                    <p className="text-gray-600">Loading detailed feedback...</p>
                  </div>
                ) : feedback ? (
                  <div className="space-y-6">
                    {/* Module-specific feedback */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      {feedback.listeningFeedback && (
                        <div className="border-l-4 border-blue-500 pl-4">
                          <h4 className="font-semibold text-blue-600 mb-2 flex items-center">
                            <Headphones className="h-4 w-4 mr-2" />
                            Listening Feedback
                          </h4>
                          <p className="text-sm text-gray-700">{feedback.listeningFeedback}</p>
                        </div>
                      )}
                      {feedback.readingFeedback && (
                        <div className="border-l-4 border-green-500 pl-4">
                          <h4 className="font-semibold text-green-600 mb-2 flex items-center">
                            <Eye className="h-4 w-4 mr-2" />
                            Reading Feedback
                          </h4>
                          <p className="text-sm text-gray-700">{feedback.readingFeedback}</p>
                        </div>
                      )}
                      {feedback.writingFeedback && (
                        <div className="border-l-4 border-yellow-500 pl-4">
                          <h4 className="font-semibold text-yellow-600 mb-2 flex items-center">
                            <PenTool className="h-4 w-4 mr-2" />
                            Writing Feedback
                          </h4>
                          <p className="text-sm text-gray-700">{feedback.writingFeedback}</p>
                        </div>
                      )}
                      {feedback.speakingFeedback && (
                        <div className="border-l-4 border-purple-500 pl-4">
                          <h4 className="font-semibold text-purple-600 mb-2 flex items-center">
                            <MessageCircle className="h-4 w-4 mr-2" />
                            Speaking Feedback
                          </h4>
                          <p className="text-sm text-gray-700">{feedback.speakingFeedback}</p>
                        </div>
                      )}
                    </div>

                    {/* Overall feedback */}
                    {feedback.overallFeedback && (
                      <div className="bg-gradient-to-r from-purple-50 to-indigo-50 rounded-lg p-6">
                        <h4 className="font-semibold text-purple-600 mb-3 flex items-center">
                          <TrendingUp className="h-5 w-5 mr-2" />
                          Overall Assessment
                        </h4>
                        <p className="text-gray-700">{feedback.overallFeedback}</p>
                      </div>
                    )}

                    {/* Study recommendations */}
                    {feedback.studyRecommendations && (
                      <div className="bg-gradient-to-r from-green-50 to-emerald-50 rounded-lg p-6">
                        <h4 className="font-semibold text-green-600 mb-3 flex items-center">
                          <GraduationCap className="h-5 w-5 mr-2" />
                          Study Recommendations
                        </h4>
                        <p className="text-gray-700">{feedback.studyRecommendations}</p>
                      </div>
                    )}

                    <div className="text-xs text-gray-500 text-center">
                      Feedback generated on {new Date(feedback.generatedAt).toLocaleDateString()}
                    </div>
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <Brain className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-600">AI feedback is not available at this time.</p>
                  </div>
                )}
              </div>
            )}

            {/* Study Guidance */}
            <div className="bg-white shadow rounded-lg p-6">
              <h2 className="text-lg font-medium text-gray-900 mb-6 flex items-center">
                <BookOpen className="h-6 w-6 text-blue-600 mr-2" />
                Personalized Study Plan
              </h2>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                <div className="bg-blue-50 rounded-lg p-6">
                  <h3 className="font-semibold text-blue-800 mb-3">Priority Areas</h3>
                  <div className="space-y-2 text-sm text-blue-700">
                    {result.writingBandScore && result.writingBandScore < 6.5 && (
                      <p>• Writing skills development (High Priority)</p>
                    )}
                    {result.speakingBandScore && result.speakingBandScore < 6.5 && (
                      <p>• Speaking fluency practice (High Priority)</p>
                    )}
                    {result.listeningBandScore && result.listeningBandScore < 6.5 && (
                      <p>• Listening comprehension (Medium Priority)</p>
                    )}
                    {result.readingBandScore && result.readingBandScore < 6.5 && (
                      <p>• Reading speed and accuracy (Medium Priority)</p>
                    )}
                    {result.overallBandScore && result.overallBandScore >= 7 && (
                      <>
                        <p>• Maintain current proficiency level</p>
                        <p>• Focus on consistency across all modules</p>
                        <p>• Consider advanced English certifications</p>
                      </>
                    )}
                    {!([result.writingBandScore, result.speakingBandScore, result.listeningBandScore, result.readingBandScore]
                      .some(score => score && score < 6.5)) && (
                      <>
                        <p>• Continue regular practice to maintain skills</p>
                        <p>• Focus on advanced vocabulary development</p>
                        <p>• Practice complex grammatical structures</p>
                      </>
                    )}
                  </div>
                </div>

                <div className="bg-green-50 rounded-lg p-6">
                  <h3 className="font-semibold text-green-800 mb-3">Recommended Study Schedule</h3>
                  <div className="space-y-2 text-sm text-green-700">
                    <p>• Daily practice: {result.overallBandScore && result.overallBandScore >= 7 ? '1-2 hours' : '2-3 hours'}</p>
                    <p>• Focus sessions: 45-60 minutes</p>
                    <p>• Weekly mock tests</p>
                    <p>• Regular progress reviews</p>
                    <p>• Vocabulary building: 15 minutes daily</p>
                    <p>• Grammar exercises: 20 minutes daily</p>
                    {result.overallBandScore && result.overallBandScore < 6 && (
                      <>
                        <p>• Foundation skills: 30 minutes daily</p>
                        <p>• Basic conversation practice</p>
                      </>
                    )}
                    {result.overallBandScore && result.overallBandScore >= 7 && (
                      <>
                        <p>• Advanced practice materials</p>
                        <p>• Academic English focus</p>
                      </>
                    )}
                  </div>
                </div>
              </div>

              {/* Next Steps */}
              <div className="bg-gradient-to-r from-purple-50 to-indigo-50 rounded-lg p-6">
                <h3 className="font-semibold text-purple-800 mb-4 flex items-center">
                  <Target className="h-5 w-5 mr-2" />
                  Your Next Steps
                </h3>
                <div className="space-y-4">
                  <div className="flex items-start space-x-3">
                    <div className="flex-shrink-0 w-8 h-8 bg-purple-600 text-white rounded-full flex items-center justify-center text-sm font-bold">1</div>
                    <div>
                      <h4 className="font-medium text-purple-900">Immediate (This Week)</h4>
                      <p className="text-sm text-purple-700">Focus on your weakest skill area and start daily practice routine</p>
                    </div>
                  </div>
                  <div className="flex items-start space-x-3">
                    <div className="flex-shrink-0 w-8 h-8 bg-purple-600 text-white rounded-full flex items-center justify-center text-sm font-bold">2</div>
                    <div>
                      <h4 className="font-medium text-purple-900">Short-term (2-4 Weeks)</h4>
                      <p className="text-sm text-purple-700">Take practice tests to track improvement and adjust study plan</p>
                    </div>
                  </div>
                  <div className="flex items-start space-x-3">
                    <div className="flex-shrink-0 w-8 h-8 bg-purple-600 text-white rounded-full flex items-center justify-center text-sm font-bold">3</div>
                    <div>
                      <h4 className="font-medium text-purple-900">Long-term (1-3 Months)</h4>
                      <p className="text-sm text-purple-700">Consider retaking IELTS when you consistently score your target band in practice tests</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Recommended Resources */}
            <div className="bg-white shadow rounded-lg p-6">
              <h2 className="text-lg font-medium text-gray-900 mb-6 flex items-center">
                <Lightbulb className="h-6 w-6 text-yellow-500 mr-2" />
                Recommended Study Resources
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                  <h3 className="font-semibold text-gray-800 mb-3">Official Materials</h3>
                  <div className="space-y-2 text-sm text-gray-600">
                    <p>• Cambridge IELTS Practice Tests (Books 1-18)</p>
                    <p>• Official IELTS Preparation Materials</p>
                    <p>• IELTS.org Practice Tests</p>
                    <p>• Cambridge English Vocabulary for IELTS</p>
                    <p>• Official IELTS Practice Materials 2</p>
                  </div>
                </div>
                <div>
                  <h3 className="font-semibold text-gray-800 mb-3">Online Platforms</h3>
                  <div className="space-y-2 text-sm text-gray-600">
                    <p>• IELTS Liz (Free lessons and tips)</p>
                    <p>• Magoosh IELTS Prep</p>
                    <p>• British Council IELTS Prep</p>
                    <p>• IDP IELTS Preparation</p>
                    <p>• IELTS Simon (Writing & Speaking)</p>
                    <p>• Road to IELTS (British Council)</p>
                  </div>
                </div>
                <div>
                  <h3 className="font-semibold text-gray-800 mb-3">Apps & Tools</h3>
                  <div className="space-y-2 text-sm text-gray-600">
                    <p>• IELTS Prep App (Official)</p>
                    <p>• Grammarly (Writing assistance)</p>
                    <p>• ELSA Speak (Pronunciation)</p>
                    <p>• Anki (Vocabulary flashcards)</p>
                    <p>• BBC Learning English</p>
                    <p>• Cambridge Dictionary</p>
                  </div>
                </div>
              </div>
            </div>
        </div>
      </main>
    </div>
  );
}

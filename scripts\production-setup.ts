/**
 * Production database setup script for Vercel deployment
 * This script should be run once after deployment to set up the database
 */

import { config } from 'dotenv';
import { drizzle } from 'drizzle-orm/postgres-js';
import postgres from 'postgres';
import bcrypt from 'bcryptjs';
import { users } from '../src/lib/db/schema';
import { eq } from 'drizzle-orm';

// Load environment variables
config({ path: '.env.local' });

async function setupProductionDatabase() {
  console.log('🚀 Starting production database setup...\n');

  // Validate environment variables
  const requiredEnvVars = ['DATABASE_URL', 'ADMIN_EMAIL', 'ADMIN_PASSWORD'];
  const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);

  if (missingVars.length > 0) {
    console.error('❌ Missing required environment variables:', missingVars.join(', '));
    process.exit(1);
  }

  const connectionString = process.env.DATABASE_URL!;
  const adminEmail = process.env.ADMIN_EMAIL!;
  const adminPassword = process.env.ADMIN_PASSWORD!;

  // Create database connection
  const client = postgres(connectionString, {
    ssl: 'require',
    max: 1,
  });
  const db = drizzle(client, { schema: { users } });

  try {
    console.log('1️⃣ Testing database connection...');
    await db.execute('SELECT 1');
    console.log('✅ Database connection successful\n');

    console.log('2️⃣ Checking if admin user exists...');
    const existingAdmin = await db
      .select()
      .from(users)
      .where(eq(users.email, adminEmail))
      .limit(1);

    if (existingAdmin.length > 0) {
      console.log('✅ Admin user already exists\n');
    } else {
      console.log('3️⃣ Creating admin user...');
      const hashedPassword = await bcrypt.hash(adminPassword, 12);

      await db.insert(users).values({
        name: 'System Administrator',
        email: adminEmail,
        password: hashedPassword,
        role: 'admin',
      });

      console.log('✅ Admin user created successfully\n');
    }

    console.log('4️⃣ Creating test checker user...');
    const testCheckerEmail = '<EMAIL>';
    const testCheckerPassword = 'checker123';

    const existingChecker = await db
      .select()
      .from(users)
      .where(eq(users.email, testCheckerEmail))
      .limit(1);

    if (existingChecker.length === 0) {
      const hashedCheckerPassword = await bcrypt.hash(testCheckerPassword, 12);

      await db.insert(users).values({
        name: 'Test Checker',
        email: testCheckerEmail,
        password: hashedCheckerPassword,
        role: 'test_checker',
      });

      console.log('✅ Test checker user created successfully\n');
    } else {
      console.log('✅ Test checker user already exists\n');
    }

    console.log('🎉 Production database setup completed!\n');
    console.log('Demo credentials:');
    console.log(`Admin: ${adminEmail} / ${adminPassword}`);
    console.log(`Test Checker: ${testCheckerEmail} / ${testCheckerPassword}\n`);

  } catch (error) {
    console.error('❌ Setup failed:', error);
    process.exit(1);
  } finally {
    await client.end();
  }
}

// Run the setup
if (require.main === module) {
  setupProductionDatabase().catch(console.error);
}

export default setupProductionDatabase;

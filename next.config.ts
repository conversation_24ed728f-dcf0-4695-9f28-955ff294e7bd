import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  serverExternalPackages: ['postgres', 'bcryptjs'],
  images: {
    domains: ['localhost'],
    remotePatterns: [
      {
        protocol: 'https',
        hostname: '*.vercel.app',
      },
      {
        protocol: 'https',
        hostname: '*.vercel-storage.com',
      },
    ],
  },
  // Webpack optimizations for Vercel
  webpack: (config, { isServer }) => {
    if (isServer) {
      config.externals.push('postgres', 'bcryptjs');
    }
    return config;
  },
};

export default nextConfig;

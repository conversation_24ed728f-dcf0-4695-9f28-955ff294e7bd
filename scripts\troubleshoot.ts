/**
 * Troubleshooting script for deployment issues
 * Run this script to diagnose common problems
 */

import { config } from 'dotenv';

// Load environment variables
config({ path: '.env.local' });

interface DiagnosticResult {
  check: string;
  status: 'pass' | 'fail' | 'warning';
  message: string;
  suggestion?: string;
}

async function runDiagnostics(): Promise<DiagnosticResult[]> {
  const results: DiagnosticResult[] = [];

  // Check environment variables
  const requiredEnvVars = [
    'DATABASE_URL',
    'NEXTAUTH_SECRET',
    'NEXTAUTH_URL',
  ];

  for (const envVar of requiredEnvVars) {
    const value = process.env[envVar];
    if (!value) {
      results.push({
        check: `Environment Variable: ${envVar}`,
        status: 'fail',
        message: `${envVar} is not set`,
        suggestion: `Set ${envVar} in your environment variables or .env.local file`
      });
    } else {
      results.push({
        check: `Environment Variable: ${envVar}`,
        status: 'pass',
        message: `${envVar} is set`
      });
    }
  }

  // Check NEXTAUTH_SECRET length
  const nextAuthSecret = process.env.NEXTAUTH_SECRET;
  if (nextAuthSecret) {
    if (nextAuthSecret.length < 32) {
      results.push({
        check: 'NEXTAUTH_SECRET Length',
        status: 'fail',
        message: `NEXTAUTH_SECRET is only ${nextAuthSecret.length} characters`,
        suggestion: 'NEXTAUTH_SECRET should be at least 32 characters. Generate with: openssl rand -base64 32'
      });
    } else {
      results.push({
        check: 'NEXTAUTH_SECRET Length',
        status: 'pass',
        message: `NEXTAUTH_SECRET is ${nextAuthSecret.length} characters (good)`
      });
    }
  }

  // Check DATABASE_URL format
  const databaseUrl = process.env.DATABASE_URL;
  if (databaseUrl) {
    if (!databaseUrl.startsWith('postgresql://') && !databaseUrl.startsWith('postgres://')) {
      results.push({
        check: 'DATABASE_URL Format',
        status: 'fail',
        message: 'DATABASE_URL does not appear to be a PostgreSQL connection string',
        suggestion: 'DATABASE_URL should start with postgresql:// or postgres://'
      });
    } else {
      results.push({
        check: 'DATABASE_URL Format',
        status: 'pass',
        message: 'DATABASE_URL format looks correct'
      });
    }

    // Check SSL requirement
    if (!databaseUrl.includes('sslmode=require') && process.env.NODE_ENV === 'production') {
      results.push({
        check: 'Database SSL',
        status: 'warning',
        message: 'DATABASE_URL does not specify SSL mode',
        suggestion: 'For production, add ?sslmode=require to your DATABASE_URL'
      });
    }
  }

  // Check NEXTAUTH_URL format
  const nextAuthUrl = process.env.NEXTAUTH_URL;
  if (nextAuthUrl) {
    try {
      const url = new URL(nextAuthUrl);
      if (url.protocol !== 'https:' && process.env.NODE_ENV === 'production') {
        results.push({
          check: 'NEXTAUTH_URL Protocol',
          status: 'warning',
          message: 'NEXTAUTH_URL is not using HTTPS',
          suggestion: 'For production, NEXTAUTH_URL should use HTTPS'
        });
      } else {
        results.push({
          check: 'NEXTAUTH_URL Format',
          status: 'pass',
          message: 'NEXTAUTH_URL format is valid'
        });
      }
    } catch {
      results.push({
        check: 'NEXTAUTH_URL Format',
        status: 'fail',
        message: 'NEXTAUTH_URL is not a valid URL',
        suggestion: 'NEXTAUTH_URL should be a complete URL like https://your-app.vercel.app'
      });
    }
  }

  // Test database connection
  try {
    const { db } = await import('../src/lib/db');
    await db.execute('SELECT 1');
    results.push({
      check: 'Database Connection',
      status: 'pass',
      message: 'Database connection successful'
    });
  } catch (error) {
    results.push({
      check: 'Database Connection',
      status: 'fail',
      message: `Database connection failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
      suggestion: 'Check your DATABASE_URL and ensure the database is accessible'
    });
  }

  return results;
}

function printResults(results: DiagnosticResult[]) {
  console.log('🔍 IELTS Certification System - Deployment Diagnostics\n');

  const passed = results.filter(r => r.status === 'pass').length;
  const failed = results.filter(r => r.status === 'fail').length;
  const warnings = results.filter(r => r.status === 'warning').length;

  for (const result of results) {
    const icon = result.status === 'pass' ? '✅' : result.status === 'fail' ? '❌' : '⚠️';
    console.log(`${icon} ${result.check}: ${result.message}`);
    if (result.suggestion) {
      console.log(`   💡 ${result.suggestion}`);
    }
    console.log();
  }

  console.log('📊 Summary:');
  console.log(`   ✅ Passed: ${passed}`);
  console.log(`   ❌ Failed: ${failed}`);
  console.log(`   ⚠️  Warnings: ${warnings}\n`);

  if (failed > 0) {
    console.log('❌ Some checks failed. Please fix the issues above before deploying.');
    process.exit(1);
  } else if (warnings > 0) {
    console.log('⚠️  Some warnings found. Consider addressing them for production deployment.');
  } else {
    console.log('🎉 All checks passed! Your configuration looks good for deployment.');
  }
}

// Run diagnostics
if (require.main === module) {
  runDiagnostics()
    .then(printResults)
    .catch(error => {
      console.error('❌ Diagnostics failed:', error);
      process.exit(1);
    });
}

export { runDiagnostics, printResults };
